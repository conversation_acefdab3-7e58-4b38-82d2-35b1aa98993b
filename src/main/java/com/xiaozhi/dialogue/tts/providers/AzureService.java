package com.xiaozhi.dialogue.tts.providers;

import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.TtsService;
import com.xiaozhi.entity.SysConfig;
import lombok.extern.slf4j.Slf4j;

import java.nio.file.Paths;

@Slf4j
public class AzureService implements TtsService {
    private static final String PROVIDER_NAME = "azure";

    private final String url;
    private final String secret;
    private final String voice;
    private final String outputPath;

    public AzureService(SysConfig config, String voice, String outputPath) {
        this.url = config.getApiUrl();
        this.secret = config.getApiSecret();
        this.voice = voice;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return "mp3";
    }

    @Override
    public String textToSpeech(Text2SpeechParams params) throws Exception {
        var filepath = Paths.get(outputPath, getAudioFileName()).toString();


        return filepath;
    }
}
